<script setup lang="ts">
defineOptions({
  name: 'DocumentForm',
})

const props = defineProps<Props>()

const emit = defineEmits<Emits>()

interface Props {
  modelValue: {
    subject: string
    chapter: string
    section: string
    questionType: string
    difficulty: string
    questionCount: number
    knowledgePoints: string[]
    otherRequirements: string
  }
}

interface Emits {
  (e: 'update:modelValue', value: Props['modelValue']): void
}

// 学科选项
const subjectOptions = [
  { label: '语文', value: 'chinese' },
  { label: '数学', value: 'math' },
  { label: '英语', value: 'english' },
  { label: '物理', value: 'physics' },
  { label: '化学', value: 'chemistry' },
  { label: '生物', value: 'biology' },
  { label: '历史', value: 'history' },
  { label: '地理', value: 'geography' },
  { label: '政治', value: 'politics' },
]

// 题目类型选项
const questionTypeOptions = [
  { label: '选择题', value: 'choice' },
  { label: '填空题', value: 'blank' },
  { label: '判断题', value: 'judge' },
  { label: '简答题', value: 'short' },
  { label: '综合题', value: 'comprehensive' },
]

// 难度选项
const difficultyOptions = [
  { label: '简单', value: 'easy' },
  { label: '中等', value: 'medium' },
  { label: '困难', value: 'hard' },
]

// 知识点选项（示例）
const knowledgePointOptions = [
  { key: '基础概念', label: '基础概念' },
  { key: '重点难点', label: '重点难点' },
  { key: '实际应用', label: '实际应用' },
  { key: '综合运用', label: '综合运用' },
]

// 计算属性用于双向绑定
const formData = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 处理知识点选择
function toggleKnowledgePoint(key: string) {
  const knowledgePoints = [...formData.value.knowledgePoints]
  const index = knowledgePoints.indexOf(key)

  if (index > -1) {
    knowledgePoints.splice(index, 1)
  }
  else {
    knowledgePoints.push(key)
  }

  formData.value = {
    ...formData.value,
    knowledgePoints,
  }
}
</script>

<template>
  <div>
    <!-- 学科选择 -->
    <NFormItem label="学科" path="subject" required>
      <template #label>
        <span class="text-left text-14px text-[#464646] font-500">学科</span>
      </template>
      <NSelect
        v-model:value="formData.subject"
        :options="subjectOptions"
        placeholder="请选择学科"
        clearable
      />
    </NFormItem>

    <!-- 章节选择 -->
    <NFormItem label="章节" path="chapter" required>
      <template #label>
        <span class="text-left text-14px text-[#464646] font-500">章节</span>
      </template>
      <NInput
        v-model:value="formData.chapter"
        placeholder="请输入章节名称"
        clearable
      />
    </NFormItem>

    <!-- 小节选择 -->
    <NFormItem label="小节" path="section">
      <template #label>
        <span class="text-left text-14px text-[#464646] font-500">小节</span>
      </template>
      <NInput
        v-model:value="formData.section"
        placeholder="请输入小节名称（可选）"
        clearable
      />
    </NFormItem>

    <!-- 知识点选择 -->
    <NFormItem label="知识点" path="knowledgePoints">
      <template #label>
        <span class="text-left text-14px text-[#464646] font-500">知识点</span>
      </template>
      <div class="flex flex-wrap gap-8px">
        <button
          v-for="option in knowledgePointOptions"
          :key="option.key"
          class="border rounded-6px px-12px py-6px text-14px font-500 transition-all duration-200"
          :class="[
            formData.knowledgePoints.includes(option.key)
              ? 'border-blue-300 bg-blue-100 text-blue-700'
              : 'border-gray-300 bg-gray-50 text-gray-600 hover:border-blue-300 hover:bg-blue-50',
          ]"
          @click="toggleKnowledgePoint(option.key)"
        >
          {{ option.label }}
        </button>
      </div>
    </NFormItem>

    <!-- 题目类型 -->
    <NFormItem label="题目类型" path="questionType" required>
      <template #label>
        <span class="text-left text-14px text-[#464646] font-500">题目类型</span>
      </template>
      <NSelect
        v-model:value="formData.questionType"
        :options="questionTypeOptions"
        placeholder="请选择题目类型"
        clearable
      />
    </NFormItem>

    <!-- 难度等级 -->
    <NFormItem label="难度等级" path="difficulty" required>
      <template #label>
        <span class="text-left text-14px text-[#464646] font-500">难度等级</span>
      </template>
      <NSelect
        v-model:value="formData.difficulty"
        :options="difficultyOptions"
        placeholder="请选择难度等级"
        clearable
      />
    </NFormItem>

    <!-- 题目数量 -->
    <NFormItem label="题目数量" path="questionCount" required>
      <template #label>
        <span class="text-left text-14px text-[#464646] font-500">题目数量</span>
      </template>
      <NInputNumber
        v-model:value="formData.questionCount"
        placeholder="请输入题目数量"
        :min="1"
        :max="50"
        class="w-full"
      />
    </NFormItem>

    <!-- 描述文本 -->
    <div class="mb-16px rounded-8px bg-purple-50 p-12px">
      <p class="text-14px text-gray-600 leading-20px">
        <SvgIcon icon="mdi:information-outline" class="mr-4px inline text-purple-500" />
        根据选择的学科章节和知识点，AI 会自动生成符合教学大纲的题目
      </p>
    </div>

    <!-- 其他要求 -->
    <NFormItem label="其他要求" path="otherRequirements">
      <template #label>
        <span class="text-left text-14px text-[#464646] font-500">其他要求</span>
      </template>
      <NInput
        v-model:value="formData.otherRequirements"
        type="textarea"
        placeholder="请输入其他特殊要求（可选）"
        :rows="3"
        clearable
        maxlength="500"
        show-count
      />
    </NFormItem>
  </div>
</template>
