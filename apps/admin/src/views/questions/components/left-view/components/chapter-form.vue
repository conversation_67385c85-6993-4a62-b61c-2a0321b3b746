<script setup lang="ts">
defineOptions({
  name: 'ChapterForm',
})

const props = defineProps<Props>()

const emit = defineEmits<Emits>()

interface Props {
  modelValue: {
    attachments: File[]
    questionType: string
    difficulty: string
    questionCount: number
    extractMethod: string
    otherRequirements: string
  }
}

interface Emits {
  (e: 'update:modelValue', value: Props['modelValue']): void
}

// 题目类型选项
const questionTypeOptions = [
  { label: '选择题', value: 'choice' },
  { label: '填空题', value: 'blank' },
  { label: '判断题', value: 'judge' },
  { label: '简答题', value: 'short' },
  { label: '综合题', value: 'comprehensive' },
]

// 难度选项
const difficultyOptions = [
  { label: '简单', value: 'easy' },
  { label: '中等', value: 'medium' },
  { label: '困难', value: 'hard' },
]

// 提取方式选项
const extractMethodOptions = [
  { label: '全文提取', value: 'full' },
  { label: '关键段落', value: 'key' },
  { label: '智能摘要', value: 'summary' },
]

// 计算属性用于双向绑定
const formData = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 处理文件上传
function handleFileChange(files: File[]) {
  formData.value = {
    ...formData.value,
    attachments: files,
  }
}

// 处理文件移除
function handleFileRemove(index: number) {
  const attachments = [...formData.value.attachments]
  attachments.splice(index, 1)
  formData.value = {
    ...formData.value,
    attachments,
  }
}
</script>

<template>
  <div>
    <!-- 文件上传 -->
    <NFormItem label="上传附件" path="attachments" required>
      <template #label>
        <span class="text-left text-14px text-[#464646] font-500">上传附件</span>
      </template>
      <NUpload
        multiple
        :max="5"
        accept=".pdf,.doc,.docx,.txt"
        @update:file-list="handleFileChange"
      >
        <NUploadDragger>
          <div class="mb-12px">
            <SvgIcon icon="mdi:cloud-upload" class="text-48px text-gray-400" />
          </div>
          <NText class="text-16px">
            点击或者拖动文件到该区域来上传
          </NText>
          <NP depth="3" class="mt-8px">
            支持 PDF、Word、TXT 格式，最多上传 5 个文件
          </NP>
        </NUploadDragger>
      </NUpload>
    </NFormItem>

    <!-- 已上传文件列表 -->
    <div v-if="formData.attachments.length > 0" class="mb-16px">
      <div class="mb-8px text-14px text-[#464646] font-500">
        已上传文件：
      </div>
      <div class="space-y-8px">
        <div
          v-for="(file, index) in formData.attachments"
          :key="index"
          class="flex items-center justify-between rounded-6px bg-gray-50 p-8px"
        >
          <div class="flex items-center gap-8px">
            <SvgIcon icon="mdi:file-document" class="text-16px text-blue-500" />
            <span class="text-14px">{{ file.name }}</span>
          </div>
          <NButton
            size="small"
            quaternary
            type="error"
            @click="handleFileRemove(index)"
          >
            <SvgIcon icon="mdi:close" />
          </NButton>
        </div>
      </div>
    </div>

    <!-- 提取方式 -->
    <NFormItem label="提取方式" path="extractMethod" required>
      <template #label>
        <span class="text-left text-14px text-[#464646] font-500">提取方式</span>
      </template>
      <NSelect
        v-model:value="formData.extractMethod"
        :options="extractMethodOptions"
        placeholder="请选择内容提取方式"
        clearable
      />
    </NFormItem>

    <!-- 题目类型 -->
    <NFormItem label="题目类型" path="questionType" required>
      <template #label>
        <span class="text-left text-14px text-[#464646] font-500">题目类型</span>
      </template>
      <NSelect
        v-model:value="formData.questionType"
        :options="questionTypeOptions"
        placeholder="请选择题目类型"
        clearable
      />
    </NFormItem>

    <!-- 难度等级 -->
    <NFormItem label="难度等级" path="difficulty" required>
      <template #label>
        <span class="text-left text-14px text-[#464646] font-500">难度等级</span>
      </template>
      <NSelect
        v-model:value="formData.difficulty"
        :options="difficultyOptions"
        placeholder="请选择难度等级"
        clearable
      />
    </NFormItem>

    <!-- 题目数量 -->
    <NFormItem label="题目数量" path="questionCount" required>
      <template #label>
        <span class="text-left text-14px text-[#464646] font-500">题目数量</span>
      </template>
      <NInputNumber
        v-model:value="formData.questionCount"
        placeholder="请输入题目数量"
        :min="1"
        :max="50"
        class="w-full"
      />
    </NFormItem>

    <!-- 描述文本 -->
    <div class="mb-16px rounded-8px bg-orange-50 p-12px">
      <p class="text-14px text-gray-600 leading-20px">
        <SvgIcon icon="mdi:information-outline" class="mr-4px inline text-orange-500" />
        上传文档附件，AI 会自动解析文档内容并根据选择的提取方式生成题目
      </p>
    </div>

    <!-- 其他要求 -->
    <NFormItem label="其他要求" path="otherRequirements">
      <template #label>
        <span class="text-left text-14px text-[#464646] font-500">其他要求</span>
      </template>
      <NInput
        v-model:value="formData.otherRequirements"
        type="textarea"
        placeholder="请输入其他特殊要求（可选）"
        :rows="3"
        clearable
        maxlength="500"
        show-count
      />
    </NFormItem>
  </div>
</template>
